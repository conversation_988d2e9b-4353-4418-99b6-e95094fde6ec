import boto3

# Create a Bedrock client
client = boto3.client('bedrock-runtime', region_name='us-east-1')

# AWS Nova Lite model ID
model_id = 'amazon.nova-lite-v1:0'

# System prompt (optional, can be customized)
system_prompt = "You are <PERSON> Li<PERSON>, a helpful AI assistant."

# Prepare the conversation messages
messages = [
    {
        "role": "user",
        "content": [
            {"text": "Hello, Nova Lite! Can you tell me a joke?"}
        ]
    }
]

# Call the model using converse API
try:
    response = client.converse(
        modelId=model_id,
        messages=messages,
        system=[{"text": system_prompt}],
        inferenceConfig={
            "maxTokens": 250,
            "temperature": 0.2
        }
    )

    # Print the response
    print("Response from Nova Lite:")
    print(response['output']['message']['content'][0]['text'])

except Exception as e:
    print(f"Error calling the model: {e}")